'use client';

import { useActionState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { UserPlus, Lock, Mail, User, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { registerUser } from '@/app/auth/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';

const initialState: AuthResult = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

export function SignUpForm() {
  const [state, formAction] = useActionState(registerUser, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();

  useEffect(() => {
    if (state.success && state.nextStep === 'verify' && state.email) {
      // Pass the email as a URL parameter to the verification page
      const verifyUrl = `/auth/verify?email=${encodeURIComponent(state.email)}`;
      router.push(verifyUrl);
    }
  }, [state.success, state.nextStep, state.email, router]);

  const displayError = state.error || csrfError;

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-[#18141f]">
      <div className="w-full max-w-sm bg-gradient-to-b from-[#262131]/50 to-[#1f1b24] rounded-3xl shadow-2xl shadow-black/25 p-8 flex flex-col items-center border border-[#374151]">
        {/* Logo/Icon */}
        <div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-[#8a2be2] mb-6 shadow-lg">
          <UserPlus className="w-7 h-7 text-white" />
        </div>

        {/* Title */}
        <h2 className="text-2xl font-semibold mb-2 text-center text-white">Join qBraid</h2>
        <p className="text-[#94a3b8] text-sm mb-6 text-center">
          Create your account to start building quantum applications
        </p>

        {/* Form */}
        <form action={formAction} className="w-full space-y-4">
          {/* CSRF Token */}
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-400 text-sm">{displayError}</p>
              </div>
            </div>
          )}

          {/* Full Name Input */}
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#6b7280]">
              <User className="w-4 h-4" />
            </span>
            <input
              name="name"
              type="text"
              placeholder="Full Name"
              required
              className="w-full pl-10 pr-3 py-3 rounded-xl border border-[#374151] focus:outline-none focus:ring-2 focus:ring-[#8a2be2]/50 focus:border-[#8a2be2] bg-[#1f1b24] text-white placeholder:text-[#6b7280] text-sm transition-all duration-200"
            />
          </div>

          {/* Email Input */}
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#6b7280]">
              <Mail className="w-4 h-4" />
            </span>
            <input
              name="email"
              type="email"
              placeholder="Email"
              required
              className="w-full pl-10 pr-3 py-3 rounded-xl border border-[#374151] focus:outline-none focus:ring-2 focus:ring-[#8a2be2]/50 focus:border-[#8a2be2] bg-[#1f1b24] text-white placeholder:text-[#6b7280] text-sm transition-all duration-200"
            />
          </div>

          {/* Password Input */}
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#6b7280]">
              <Lock className="w-4 h-4" />
            </span>
            <input
              name="password"
              type="password"
              placeholder="Password (min. 8 characters)"
              required
              className="w-full pl-10 pr-3 py-3 rounded-xl border border-[#374151] focus:outline-none focus:ring-2 focus:ring-[#8a2be2]/50 focus:border-[#8a2be2] bg-[#1f1b24] text-white placeholder:text-[#6b7280] text-sm transition-all duration-200"
            />
          </div>

          {/* Confirm Password Input */}
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#6b7280]">
              <Lock className="w-4 h-4" />
            </span>
            <input
              name="confirmPassword"
              type="password"
              placeholder="Confirm Password"
              required
              className="w-full pl-10 pr-3 py-3 rounded-xl border border-[#374151] focus:outline-none focus:ring-2 focus:ring-[#8a2be2]/50 focus:border-[#8a2be2] bg-[#1f1b24] text-white placeholder:text-[#6b7280] text-sm transition-all duration-200"
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken}
            className="w-full bg-gradient-to-r from-[#8a2be2] to-[#7c2dd5] text-white font-medium py-3 rounded-xl shadow-lg hover:shadow-xl hover:shadow-[#8a2be2]/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] mt-6"
          >
            {csrfLoading ? 'Loading...' : 'Create Account'}
          </button>
        </form>

        {/* Divider */}
        <div className="flex items-center w-full my-6">
          <div className="flex-grow border-t border-dashed border-[#374151]"></div>
          <span className="mx-3 text-xs text-[#6b7280]">Or sign up with</span>
          <div className="flex-grow border-t border-dashed border-[#374151]"></div>
        </div>

        {/* Social Sign Up Buttons */}
        <div className="flex gap-3 w-full justify-center">
          <button
            type="button"
            className="flex items-center justify-center w-12 h-12 rounded-xl border border-[#374151] bg-[#262131] hover:bg-[#2a2631] transition-colors duration-200 grow"
          >
            <img
              src="https://www.svgrepo.com/show/475656/google-color.svg"
              alt="Google"
              className="w-5 h-5"
            />
          </button>
          <button
            type="button"
            className="flex items-center justify-center w-12 h-12 rounded-xl border border-[#374151] bg-[#262131] hover:bg-[#2a2631] transition-colors duration-200 grow"
          >
            <img
              src="https://www.svgrepo.com/show/448224/facebook.svg"
              alt="Facebook"
              className="w-5 h-5"
            />
          </button>
          <button
            type="button"
            className="flex items-center justify-center w-12 h-12 rounded-xl border border-[#374151] bg-[#262131] hover:bg-[#2a2631] transition-colors duration-200 grow"
          >
            <img
              src="https://www.svgrepo.com/show/511330/apple-173.svg"
              alt="Apple"
              className="w-5 h-5"
            />
          </button>
        </div>

        {/* Sign In Link */}
        <div className="mt-6 text-center">
          <span className="text-[#94a3b8] text-sm">
            Already have an account?{' '}
            <Link
              href="/auth/signin"
              className="text-[#8a2be2] hover:text-[#7c2dd5] hover:underline font-medium transition-colors duration-200"
            >
              Sign in
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
}
