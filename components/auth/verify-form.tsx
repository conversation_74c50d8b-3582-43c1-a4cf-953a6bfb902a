'use client';

import { useActionState } from 'react';
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Link from 'next/link';
import { verifyEmail } from '@/app/auth/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';

const initialState: AuthResult = {
  success: false,
  error: undefined,
};

export function VerifyForm() {
  const [state, formAction] = useActionState(verifyEmail, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState('');

  // Get email from URL parameters on component mount
  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  useEffect(() => {
    if (state.success) {
      // If redirectTo is provided (auto-login), use that, otherwise go to sign-in
      const redirectPath = state.redirectTo || '/auth/signin';
      router.push(redirectPath);
      if (state.redirectTo) {
        // Force refresh to ensure auth state is updated
        router.refresh();
      }
    }
  }, [state.success, state.redirectTo, router]);

  return (
    <div className="grid gap-6">
      <form action={formAction} className="space-y-4">
        {/* CSRF Token */}
        <CSRFTokenInput csrfToken={csrfToken} />

        {/* Show CSRF error if any */}
        {csrfError && (
          <Alert variant="destructive">
            <AlertDescription>
              Security token error: {csrfError}. Please refresh the page.
            </AlertDescription>
          </Alert>
        )}

        {state.error && (
          <Alert variant={state.redirectTo ? 'default' : 'destructive'}>
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled
          />
          {/* Hidden input to ensure email is submitted */}
          <input type="hidden" name="email" value={email} />
          {email && (
            <p className="text-xs text-muted-foreground">
              Verification code sent to this email address
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="code">Verification Code</Label>
          <Input
            id="code"
            name="code"
            type="text"
            placeholder="Enter 6-digit code"
            maxLength={6}
            autoComplete="one-time-code"
            required
          />
        </div>

        <Button 
          type="submit" 
          className="w-full"
          disabled={csrfLoading || !csrfToken}
        >
          {csrfLoading ? 'Loading...' : 'Verify Email'}
        </Button>
      </form>

      <div className="text-center text-sm">
        <span>
          Didn't receive a code?{' '}
          <Link href="/auth/signup" className="text-primary hover:underline">
            Try signing up again
          </Link>
        </span>
      </div>
    </div>
  );
}
