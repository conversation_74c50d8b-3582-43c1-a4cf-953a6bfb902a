'use client';

import { useActionState, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { LogIn, Mail, Lock, AlertCircle, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { authenticateUser } from '@/app/auth/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';

const initialState: AuthResult = {
  success: false,
  error: undefined,
  requiresVerification: false,
  redirectTo: undefined,
};

export function SignInForm() {
  const [state, formAction] = useActionState(authenticateUser, initialState);
  const [email, setEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/';

  useEffect(() => {
    console.log('Sign-in form state updated:', state);
    if (state.success) {
      console.log('Authentication successful, redirecting to:', state.redirectTo || redirect);
      const redirectPath = state.redirectTo || redirect;
      router.push(redirectPath);
      router.refresh();
    }
  }, [state.success, state.redirectTo, router, redirect]);

  const displayError = state.error || csrfError;

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-[#0a0a0b] p-4">
      <div className="w-full max-w-md bg-[#111113] rounded-2xl border border-[#1a1a1d] shadow-2xl">
        {/* Header */}
        <div className="px-8 pt-12 pb-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-[#8b5cf6] to-[#7c3aed] mb-8 shadow-lg shadow-[#8b5cf6]/25">
            <LogIn className="w-8 h-8 text-white" />
          </div>

          <h1 className="text-3xl font-bold text-white mb-3 tracking-tight">Welcome back</h1>
          <p className="text-[#9ca3af] text-base leading-relaxed">Sign in to your qBraid account</p>
        </div>

        {/* Form */}
        <div className="px-8 pb-12">
          <form action={formAction} className="space-y-6">
            {/* CSRF Token */}
            <CSRFTokenInput csrfToken={csrfToken} />

            {/* Error Alert */}
            {displayError && (
              <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-red-400 text-sm leading-relaxed">
                    {displayError}
                    {state.requiresVerification && (
                      <>
                        <br />
                        <Link
                          href={`/auth/verify${email ? `?email=${encodeURIComponent(email)}` : ''}`}
                          className="underline hover:no-underline font-medium"
                        >
                          Go to verification page →
                        </Link>
                      </>
                    )}
                  </p>
                </div>
              </div>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-[#e5e7eb]">
                Email address
              </label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-[#6b7280]" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full pl-12 pr-4 py-4 bg-[#1a1a1d] border border-[#2a2a2d] rounded-xl text-white placeholder:text-[#6b7280] focus:outline-none focus:ring-2 focus:ring-[#8b5cf6]/50 focus:border-[#8b5cf6] transition-all duration-200 text-base"
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium text-[#e5e7eb]">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-[#6b7280]" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  placeholder="Enter your password"
                  required
                  className="w-full pl-12 pr-12 py-4 bg-[#1a1a1d] border border-[#2a2a2d] rounded-xl text-white placeholder:text-[#6b7280] focus:outline-none focus:ring-2 focus:ring-[#8b5cf6]/50 focus:border-[#8b5cf6] transition-all duration-200 text-base"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-[#6b7280] hover:text-[#9ca3af] transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Forgot Password */}
            <div className="flex justify-end">
              <Link
                href="/auth/forgot-password"
                className="text-sm text-[#8b5cf6] hover:text-[#7c3aed] font-medium transition-colors"
              >
                Forgot password?
              </Link>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={csrfLoading || !csrfToken}
              className="w-full bg-gradient-to-r from-[#8b5cf6] to-[#7c3aed] text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-[#8b5cf6]/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] text-base"
            >
              {csrfLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  Loading...
                </div>
              ) : (
                'Sign in'
              )}
            </button>

            {/* Divider */}
            <div className="relative my-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-[#2a2a2d]" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-[#111113] text-[#6b7280] font-medium">
                  Or continue with
                </span>
              </div>
            </div>

            {/* Google Sign In */}
            <button
              type="button"
              className="w-full flex items-center justify-center gap-3 px-4 py-4 bg-[#1a1a1d] border border-[#2a2a2d] rounded-xl text-[#e5e7eb] font-medium hover:bg-[#212124] hover:border-[#3a3a3d] transition-all duration-200 text-base"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </button>
          </form>

          {/* Sign Up Link */}
          <div className="mt-8 pt-6 border-t border-[#2a2a2d] text-center">
            <p className="text-[#9ca3af]">
              Don't have an account?{' '}
              <Link
                href="/auth/signup"
                className="text-[#8b5cf6] hover:text-[#7c3aed] font-semibold transition-colors"
              >
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
