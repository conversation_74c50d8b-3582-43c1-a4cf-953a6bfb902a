'use client';

import { Button } from '@/components/ui/button';
import { logout } from '@/app/auth/actions';

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

export function LogoutButton({
  variant = 'outline',
  size = 'default',
  className,
  children = 'Sign out',
}: LogoutButtonProps) {
  const handleLogout = async () => {
    await logout();
  };

  return (
    <Button variant={variant} size={size} className={className} onClick={handleLogout}>
      {children}
    </Button>
  );
}
