'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Github, Linkedin, X as Twitter } from 'lucide-react';

export default function Footer() {
  const pathname = usePathname();

  // Hide footer on auth pages
  if (pathname.startsWith('/auth')) {
    return null;
  }

  return (
    <footer className="bg-[#18141f] border-t border-[#2a2a2d] py-8 px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-violet-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">q</span>
              </div>
              <span className="text-white font-semibold text-lg">qBraid</span>
            </div>
            <p className="text-gray-400 text-sm max-w-md">
              Empowering quantum computing research and development with enterprise-grade tools and
              infrastructure.
            </p>
          </div>

          {/* Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/devices"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Devices
                </Link>
              </li>
              <li>
                <Link
                  href="/earnings"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Earnings
                </Link>
              </li>
              <li>
                <Link
                  href="/team"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Team
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/docs"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Documentation
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Terms
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy"
                  className="text-gray-400 hover:text-white transition-colors text-sm"
                >
                  Privacy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[#2a2a2d] mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">© 2025 qBraid Co.</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="/docs" className="text-gray-400 hover:text-white transition-colors text-sm">
              Docs
            </Link>
            <Link
              href="/careers"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Careers
            </Link>
            <Link
              href="/terms"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Terms & Conditions
            </Link>
            <Link
              href="/privacy"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
