import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import type { ActionLogRowProps } from '@/types/logs';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string }> },
) {
  try {
    const { provider } = await params;
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    if (!provider) {
      return NextResponse.json(
        {
          error: 'Provider is required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    const response = await externalClient.get(
      `/audit-logs/${provider}?page=${page}&resultsPerPage=${resultsPerPage}`,
    );

    const data = response.data;

    // Transform timestamps
    data.auditLogsArray = data.auditLogsArray.map((log: ActionLogRowProps) => {
      if (log.createdAt) {
        log.createdAt = new Date(log.createdAt);
      }
      return log;
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching audit logs:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch audit logs',
        auditLogsArray: [],
      },
      { status: error.status || 500 },
    );
  }
}
