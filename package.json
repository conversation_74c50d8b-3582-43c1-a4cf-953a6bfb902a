{"name": "partner-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pretty-all": "prettier --write .", "test": "vitest --ui --coverage", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "cy:open": "cypress open", "cy:run": "cypress run"}, "dependencies": {"@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "autoprefixer": "^10.4.20", "aws-amplify": "^6.15.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "embla-carousel-react": "8.5.1", "formik": "^2.4.6", "jose": "^6.0.11", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "papaparse": "^5.5.3", "react": "^19", "react-dom": "^19", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "yup": "^1.6.1", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "cypress": "^14.5.1", "jsdom": "^26.1.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.2.4"}}